# X-ZoneServers SEO and Performance Optimization
# Advanced .htaccess configuration for enterprise hosting

# ======================================
# SECURITY HEADERS FOR SEO TRUST SIGNALS
# ======================================

# Content Security Policy - Google 2025 Enhanced
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://unpkg.com https://fonts.googleapis.com https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests"

# Google 2025 Advanced Security Headers
Header always set X-Content-Type-Options "nosniff"
Header always set X-Frame-Options "DENY"
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), fullscreen=(self)"

# Cross-Origin Policies - Google 2025 Security Signals
Header always set Cross-Origin-Embedder-Policy "require-corp"
Header always set Cross-Origin-Opener-Policy "same-origin"
Header always set Cross-Origin-Resource-Policy "same-origin"

# Certificate Transparency - Google 2025 Trust Signal
Header always set Expect-CT "max-age=86400, enforce"

# HSTS (HTTP Strict Transport Security) - Enhanced
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

# Server Information Security
Header always unset Server
Header always unset X-Powered-By
Header always set Server "X-ZoneServers-Enterprise"

# ======================================
# PERFORMANCE OPTIMIZATION
# ======================================

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/ld+json
</IfModule>

# Browser caching for performance
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/eot "access plus 1 year"
    
    # HTML and XML
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/ld+json "access plus 1 hour"
    
    # Manifest files
    ExpiresByType application/manifest+json "access plus 1 week"
    
    # Default
    ExpiresDefault "access plus 1 month"
</IfModule>

# ======================================
# SEO OPTIMIZATION
# ======================================

# Canonical URL enforcement (remove www if present)
RewriteEngine On
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Trailing slash normalization
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !(.*)/$
RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1/ [L,R=301]

# Clean URLs (remove .html extension)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.html [NC,L]

# Remove .html from URLs
RewriteCond %{THE_REQUEST} \s/+(.*/)?([^./\s]+)\.html[\s?] [NC]
RewriteRule ^ /%1%2? [R=301,L]

# ======================================
# XML SITEMAP OPTIMIZATION
# ======================================

# Set proper MIME type for XML sitemap
<Files "sitemap.xml">
    Header set Content-Type "application/xml; charset=utf-8"
</Files>

# ======================================
# ERROR PAGE OPTIMIZATION
# ======================================

# Custom error pages with proper HTTP status codes
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# ======================================
# CORS HEADERS FOR API ENDPOINTS
# ======================================

<IfModule mod_headers.c>
    # Allow cross-origin requests for API endpoints
    <FilesMatch "\.(json|js)$">
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
        Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
    </FilesMatch>
</IfModule>

# ======================================
# BLOCK BAD BOTS AND SCRAPERS
# ======================================

# Block harmful bots while allowing good SEO bots
RewriteCond %{HTTP_USER_AGENT} (AhrefsBot|MJ12bot|DotBot|SemrushBot|ScreamingFrog) [NC]
RewriteRule .* - [F,L]

# ======================================
# OPTIMIZE FONTS AND RESOURCES
# ======================================

# Add proper headers for web fonts
<FilesMatch "\.(woff|woff2|ttf|eot)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Cache-Control "public, max-age=31536000, immutable"
</FilesMatch>

# ======================================
# STRUCTURED DATA OPTIMIZATION
# ======================================

# Ensure JSON-LD is served with correct MIME type
<FilesMatch "\.json$">
    Header set Content-Type "application/ld+json; charset=utf-8"
</FilesMatch>

# ======================================
# MOBILE AND PWA OPTIMIZATION
# ======================================

# Proper MIME types for manifest and service worker
<Files "manifest.json">
    Header set Content-Type "application/manifest+json; charset=utf-8"
</Files>

<Files "sw.js">
    Header set Content-Type "application/javascript; charset=utf-8"
    Header set Cache-Control "no-cache, no-store, must-revalidate"
</Files>

# ======================================
# PRELOAD OPTIMIZATION
# ======================================

# Add preload headers for critical resources
<FilesMatch "\.(css)$">
    Header add Link "</style.css>; rel=preload; as=style"
</FilesMatch>

# ======================================
# BLOG URL ROUTING
# ======================================

# Route blog posts: /blog/post-slug/ -> blog-display.php
RewriteRule ^blog/([a-z0-9-]+)/?$ blog-display.php [L,QSA]

# Route blog categories: /blog/category/ -> blog-display.php
RewriteRule ^blog/(server-administration|network-optimization|security-compliance|case-studies)/?$ blog-display.php [L,QSA]

# Route blog index: /blog/ -> blog-display.php
RewriteRule ^blog/?$ blog-display.php [L,QSA]

# Route blog admin: /blog-admin/ -> blog-admin.php
RewriteRule ^blog-admin/?$ blog-admin.php [L,QSA]

# Route blog sitemap: /blog-sitemap.xml -> blog-sitemap.php
RewriteRule ^blog-sitemap\.xml$ blog-sitemap.php [L]

# ======================================
# SECURITY FOR SENSITIVE FILES
# ======================================

# Block access to WHMCS configuration and module files
<FilesMatch "^(whmcs-config\.php|whmcs-blog-module\.php|whmcs-ticket-handler\.php)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to blog admin from unauthorized IPs (uncomment and add your IPs)
# <Files "blog-admin.php">
#     Order deny,allow
#     Deny from all
#     Allow from 127.0.0.1
#     Allow from YOUR.IP.ADDRESS.HERE
# </Files>

# ======================================
# CHARSET DEFINITION
# ======================================

AddDefaultCharset UTF-8