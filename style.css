        body {
            font-family: 'Inter', sans-serif;
            background-color: #020617; /* slate-950 */
            color: #d1d5db; /* gray-300 */
        }
        
        /* Google 2025 Accessibility Excellence - Skip Links */
        .skip-link {
            position: absolute;
            left: -10000px;
            top: auto;
            width: 1px;
            height: 1px;
            overflow: hidden;
            background: #2563eb;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            transition: all 0.2s ease;
        }
        
        .skip-link:focus {
            position: fixed;
            left: 1rem;
            top: 1rem;
            width: auto;
            height: auto;
            clip: auto;
            overflow: visible;
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }
        
        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            .skip-link {
                background: #000000;
                border: 2px solid #ffffff;
            }
        }
        
        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            .skip-link {
                transition: none;
            }
        }
        
        /* Mobile Navigation Improvements */
        .hamburger-line {
            transform-origin: center;
        }
        
        /* Tablet and Mobile Responsive Fixes */
        @media (max-width: 1023px) {
            /* Ensure mobile menu button is visible on tablet */
            #mobile-menu-button {
                display: flex !important;
            }
            
            /* Hide desktop navigation on tablet and mobile */
            nav.lg\\:flex {
                display: none !important;
            }
            
            /* Adjust header padding for smaller screens */
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
        
        @media (max-width: 768px) {
            /* Mobile-specific adjustments */
            .h-20 {
                height: 4rem; /* Reduce header height on mobile */
            }
            
            /* Ensure text doesn't overlap */
            .text-2xl {
                font-size: 1.25rem;
            }
        }
        
        /* Mobile Menu Animation */
        #mobile-menu {
            transition: all 0.3s ease-in-out;
        }
        
        /* Fix mobile menu z-index */
        header {
            z-index: 9999 !important;
        }
        
        #mobile-menu {
            z-index: 9998 !important;
        }
        
        /* Remove any gap between header and main content */
        main {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        .hero-gradient {
            background: radial-gradient(ellipse at top, rgba(14, 165, 233, 0.15), transparent 60%);
        }
        .feature-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(15, 23, 42, 0.5); /* slate-900 with opacity */
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            border: 1px solid rgba(56, 189, 248, 0.5); /* sky-400 with opacity */
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(14, 165, 233, 0.1);
        }
        .pricing-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: #0f172a; /* slate-900 */
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .pricing-card:hover {
            border-color: #38bdf8;
        }
        .pricing-card .card-content {
            flex-grow: 1;
        }
        .btn-primary {
            background-color: #0ea5e9; /* sky-500 */
            color: white;
            transition: background-color 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #38bdf8; /* sky-400 */
        }
        .btn-secondary {
            background-color: transparent;
            border: 1px solid #38bdf8;
            color: #38bdf8;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background-color: #38bdf8;
            color: white;
        }
        .faq-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .faq-item summary {
            cursor: pointer;
            outline: none;
        }
        .faq-item[open] summary {
           color: #38bdf8;
        }